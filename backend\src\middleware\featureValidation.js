const featureService = require('../services/featureService');

/**
 * Middleware to validate workspace features before allowing actions
 * @param {string} action - The action to validate ('add_member', 'add_board', 'use_labels', 'use_team')
 * @param {string} workspaceIdParam - The parameter name containing workspace ID (default: 'workspaceId')
 */
const validateFeature = (action, workspaceIdParam = 'workspaceId') => {
  return async (req, res, next) => {
    try {
      // Extract workspace ID from request
      let workspaceId = req.params[workspaceIdParam] || req.body[workspaceIdParam];
      
      // For board-related actions, we might need to get workspace ID from board
      if (!workspaceId && action.includes('board') && req.body.boardId) {
        const { Board } = require('../models');
        const board = await Board.findByPk(req.body.boardId);
        if (board) {
          workspaceId = board.workspaceId;
        }
      }
      
      if (!workspaceId) {
        return res.status(400).json({
          message: 'Workspace ID is required for feature validation',
          error: 'MISSING_WORKSPACE_ID'
        });
      }

      // Validate the action
      const validation = await featureService.validateAction(workspaceId, action);
      
      if (!validation.allowed) {
        return res.status(403).json({
          message: validation.reason,
          error: 'FEATURE_NOT_AVAILABLE',
          upgradeRequired: validation.upgradeRequired,
          currentPackage: validation.currentPackage,
          featureStatus: validation.featureStatus
        });
      }

      // Add feature status to request for use in controllers
      req.featureStatus = validation.featureStatus;
      next();
    } catch (error) {
      console.error('Feature validation error:', error);
      return res.status(500).json({
        message: 'Error validating feature access',
        error: 'FEATURE_VALIDATION_ERROR'
      });
    }
  };
};

/**
 * Middleware to check if team features are enabled
 */
const requireTeamFeature = validateFeature('use_team');

/**
 * Middleware to check if label features are enabled
 */
const requireLabelFeature = validateFeature('use_labels');

/**
 * Middleware to check if workspace can add more members
 */
const requireMemberLimit = validateFeature('add_member');

/**
 * Middleware to check if workspace can add more boards
 */
const requireBoardLimit = validateFeature('add_board');

/**
 * Middleware to attach workspace feature status to request
 * Useful for endpoints that need to return feature information
 */
const attachFeatureStatus = (workspaceIdParam = 'workspaceId') => {
  return async (req, res, next) => {
    try {
      const workspaceId = req.params[workspaceIdParam] || req.body[workspaceIdParam];
      
      if (!workspaceId) {
        return next(); // Skip if no workspace ID
      }

      const featureStatus = await featureService.getWorkspaceFeatureStatus(workspaceId);
      req.featureStatus = featureStatus;
      next();
    } catch (error) {
      console.error('Error attaching feature status:', error);
      // Don't fail the request, just continue without feature status
      next();
    }
  };
};

/**
 * Helper function to create custom feature validation middleware
 * @param {string} feature - Feature name ('team', 'label')
 * @param {string} workspaceIdParam - Parameter name for workspace ID
 */
const createFeatureValidator = (feature, workspaceIdParam = 'workspaceId') => {
  return async (req, res, next) => {
    try {
      const workspaceId = req.params[workspaceIdParam] || req.body[workspaceIdParam];
      
      if (!workspaceId) {
        return res.status(400).json({
          message: 'Workspace ID is required',
          error: 'MISSING_WORKSPACE_ID'
        });
      }

      const isEnabled = await featureService.isFeatureEnabled(workspaceId, feature);
      
      if (!isEnabled) {
        const config = await featureService.getPackageConfig(
          (await require('../models').Workspace.findByPk(workspaceId)).packageId
        );
        
        return res.status(403).json({
          message: `${feature} feature is not available in ${config.name} plan`,
          error: 'FEATURE_NOT_AVAILABLE',
          feature,
          currentPackage: config.name,
          upgradeRequired: true
        });
      }

      next();
    } catch (error) {
      console.error(`${feature} feature validation error:`, error);
      return res.status(500).json({
        message: `Error validating ${feature} feature access`,
        error: 'FEATURE_VALIDATION_ERROR'
      });
    }
  };
};

module.exports = {
  validateFeature,
  requireTeamFeature,
  requireLabelFeature,
  requireMemberLimit,
  requireBoardLimit,
  attachFeatureStatus,
  createFeatureValidator
};
